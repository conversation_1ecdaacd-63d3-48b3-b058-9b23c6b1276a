version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ASSEMBLYAI_API_KEY=${ASSEMBLYAI_API_KEY}
      - UPLOAD_DIR=/app/temp_uploads
      - MAX_FILE_SIZE=100000000
      - CORS_ORIGINS=http://localhost:5173,http://localhost:3000
      - API_HOST=0.0.0.0
      - API_PORT=8000
    volumes:
      - ./backend/temp_uploads:/app/temp_uploads
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  uploads:
